<script setup>
import { ref, onMounted } from 'vue'

const cards = ref([
  {
    id: 1,
    title: 'Liquid Glass Design',
    subtitle: '流动的透明质感',
    description: '体验苹果最新的液态玻璃视觉效果，融合了半透明材质与动态流动动画。',
    icon: '💧'
  },
  {
    id: 2,
    title: 'Dynamic Reflections',
    subtitle: '动态光线反射',
    description: '智能光线折射系统，根据用户交互实时调整反射角度和强度。',
    icon: '✨'
  },
  {
    id: 3,
    title: 'Depth Perception',
    subtitle: '立体深度感知',
    description: '多层次渐变和模糊效果，创造出令人惊叹的三维视觉深度。',
    icon: '🌊'
  },
  {
    id: 4,
    title: 'Interactive Elements',
    subtitle: '交互式元素',
    description: '响应式设计支持触摸和鼠标交互，带来沉浸式用户体验。',
    icon: '🎯'
  }
])

const mousePosition = ref({ x: 0, y: 0 })

const handleMouseMove = (event) => {
  mousePosition.value = {
    x: (event.clientX / window.innerWidth) * 100,
    y: (event.clientY / window.innerHeight) * 100
  }
}

onMounted(() => {
  window.addEventListener('mousemove', handleMouseMove)
})
</script>

<template>
  <div class="liquid-glass-app">
    <!-- 背景动态渐变 -->
    <div class="background-gradient" :style="{
      '--mouse-x': mousePosition.x + '%',
      '--mouse-y': mousePosition.y + '%'
    }"></div>

    <!-- 浮动粒子效果 -->
    <div class="floating-particles">
      <div v-for="i in 20" :key="i" class="particle" :style="{
        '--delay': (i * 0.5) + 's',
        '--duration': (3 + Math.random() * 4) + 's'
      }"></div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 标题区域 -->
      <header class="header-section">
        <div class="glass-panel header-panel">
          <h1 class="main-title">
            <span class="title-gradient">Liquid Glass</span>
          </h1>
          <p class="subtitle">苹果最新视觉设计语言</p>
          <div class="title-decoration"></div>
        </div>
      </header>

      <!-- 卡片网格 -->
      <main class="cards-grid">
        <div
          v-for="card in cards"
          :key="card.id"
          class="glass-card"
          @mouseenter="$event.target.classList.add('hovered')"
          @mouseleave="$event.target.classList.remove('hovered')"
        >
          <div class="card-content">
            <div class="card-icon">{{ card.icon }}</div>
            <h3 class="card-title">{{ card.title }}</h3>
            <p class="card-subtitle">{{ card.subtitle }}</p>
            <p class="card-description">{{ card.description }}</p>
            <button class="glass-button">
              <span>了解更多</span>
              <div class="button-ripple"></div>
            </button>
          </div>
          <div class="card-glow"></div>
          <div class="liquid-flow"></div>
        </div>
      </main>

      <!-- 底部交互区域 -->
      <footer class="footer-section">
        <div class="glass-panel footer-panel">
          <div class="feature-highlights">
            <div class="highlight-item">
              <div class="highlight-icon">🎨</div>
              <span>视觉设计</span>
            </div>
            <div class="highlight-item">
              <div class="highlight-icon">⚡</div>
              <span>高性能</span>
            </div>
            <div class="highlight-item">
              <div class="highlight-icon">📱</div>
              <span>响应式</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<style scoped>
/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.liquid-glass-app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: #ffffff;
}

/* 动态背景渐变 */
.background-gradient {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
      rgba(147, 51, 234, 0.3) 0%,
      rgba(59, 130, 246, 0.2) 25%,
      rgba(16, 185, 129, 0.1) 50%,
      rgba(0, 0, 0, 0.8) 100%),
    linear-gradient(135deg,
      #0f0f23 0%,
      #1a1a2e 25%,
      #16213e 50%,
      #0f3460 75%,
      #533483 100%);
  animation: gradientShift 20s ease-in-out infinite;
  z-index: -2;
}

@keyframes gradientShift {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  25% { filter: hue-rotate(90deg) brightness(1.1); }
  50% { filter: hue-rotate(180deg) brightness(0.9); }
  75% { filter: hue-rotate(270deg) brightness(1.1); }
}

/* 浮动粒子效果 */
.floating-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  animation: float var(--duration, 4s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
  left: calc(10% + 80% * var(--random-x, 0.5));
  top: calc(10% + 80% * var(--random-y, 0.5));
}

.particle:nth-child(odd) {
  --random-x: 0.2;
  --random-y: 0.8;
}

.particle:nth-child(even) {
  --random-x: 0.7;
  --random-y: 0.3;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) translateX(-15px) scale(0.8);
    opacity: 0.5;
  }
  75% {
    transform: translateY(-30px) translateX(5px) scale(1.1);
    opacity: 0.7;
  }
}

/* 主容器 */
.main-container {
  position: relative;
  z-index: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* 玻璃面板基础样式 */
.glass-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  position: relative;
  overflow: hidden;
}

.glass-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%);
}

.glass-panel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

/* 标题区域样式 */
.header-section {
  margin-bottom: 3rem;
}

.header-panel {
  padding: 3rem 2rem;
  text-align: center;
  position: relative;
}

.main-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 1rem;
  position: relative;
}

.title-gradient {
  background: linear-gradient(135deg,
    #ffffff 0%,
    #a78bfa 25%,
    #06b6d4 50%,
    #10b981 75%,
    #ffffff 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleShimmer 3s ease-in-out infinite;
}

@keyframes titleShimmer {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 2rem;
  font-weight: 300;
}

.title-decoration {
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #06b6d4, transparent);
  margin: 0 auto;
  border-radius: 1px;
  animation: decorationPulse 2s ease-in-out infinite;
}

@keyframes decorationPulse {
  0%, 100% { opacity: 0.5; transform: scaleX(1); }
  50% { opacity: 1; transform: scaleX(1.5); }
}

/* 卡片网格布局 */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* 玻璃卡片样式 */
.glass-card {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  min-height: 300px;
  display: flex;
  flex-direction: column;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
}

.glass-card:hover,
.glass-card.hovered {
  transform: translateY(-8px) scale(1.02);
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.glass-card:hover .card-glow,
.glass-card.hovered .card-glow {
  opacity: 1;
  transform: scale(1.1);
}

.glass-card:hover .liquid-flow,
.glass-card.hovered .liquid-flow {
  animation-play-state: running;
}

/* 卡片发光效果 */
.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle,
    rgba(147, 51, 234, 0.3) 0%,
    rgba(59, 130, 246, 0.2) 30%,
    transparent 70%);
  opacity: 0;
  transition: all 0.6s ease;
  pointer-events: none;
  z-index: -1;
}

/* 液体流动效果 */
.liquid-flow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%);
  animation: liquidFlow 3s ease-in-out infinite;
  animation-play-state: paused;
  pointer-events: none;
}

@keyframes liquidFlow {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 卡片内容样式 */
.card-content {
  position: relative;
  z-index: 2;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: block;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #ffffff;
}

.card-subtitle {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
  font-weight: 300;
}

.card-description {
  font-size: 0.95rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 2rem;
  flex: 1;
}

/* 玻璃按钮样式 */
.glass-button {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  color: #ffffff;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  align-self: flex-start;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.glass-button span {
  position: relative;
  z-index: 2;
}

.button-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  pointer-events: none;
}

.glass-button:active .button-ripple {
  width: 200px;
  height: 200px;
}

/* 底部区域样式 */
.footer-section {
  margin-top: 2rem;
}

.footer-panel {
  padding: 2rem;
}

.feature-highlights {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  min-width: 120px;
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-4px);
}

.highlight-icon {
  font-size: 2rem;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.highlight-item span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    padding: 1rem;
  }

  .header-panel {
    padding: 2rem 1rem;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .glass-card {
    padding: 1.5rem;
    min-height: 250px;
  }

  .feature-highlights {
    gap: 1.5rem;
  }

  .highlight-item {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .glass-card {
    padding: 1rem;
  }

  .card-icon {
    font-size: 2.5rem;
  }

  .card-title {
    font-size: 1.3rem;
  }
}

/* 性能优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>